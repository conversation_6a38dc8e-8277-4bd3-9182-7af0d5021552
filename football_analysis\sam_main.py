from utils import read_video, save_video
from sam_extractor.sam_coordinate_extractor import SAMCoordinateExtractor
import cv2
import numpy as np

def main():
    import os

    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Initialize SAM coordinate extractor
    # Note: sam_checkpoint_path is now ignored as we use Ultralytics SAM which auto-downloads
    sam_extractor = SAMCoordinateExtractor(
        sam_checkpoint_path='models/sam_vit_h_4b8939.pth',  # Ignored - using Ultralytics SAM
        yolo_model_path=os.path.join(script_dir, 'models', 'best.pt'),
        model_type='vit_h'  # Will use sam_b.pt from Ultralytics
    )

    # Read video frames
    video_frames = read_video(os.path.join(script_dir, 'input_videos', '2e57b9_0.mp4'))

    # Extract coordinates using SAM
    print("Extracting coordinates with SAM...")
    coordinates = sam_extractor.extract_coordinates_from_frames(
        video_frames[:100],  # Process first 100 frames for demo
        save_stub=True,
        stub_path=os.path.join(script_dir, 'stubs', 'sam_coordinates.pkl')
    )

    # Visualize results
    output_frames = []
    for frame_idx, frame in enumerate(video_frames[:100]):
        vis_frame = sam_extractor.visualize_coordinates(frame, coordinates, frame_idx)
        output_frames.append(vis_frame)

    # Save output video
    save_video(output_frames, os.path.join(script_dir, 'output_videos', 'sam_coordinates_output.avi'))
    
    # Print summary
    print(f"Processed {len(video_frames[:100])} frames")
    print(f"Average players per frame: {np.mean([len(players) for players in coordinates['players']]):.2f}")
    print(f"Ball detected in {sum(1 for balls in coordinates['ball'] if balls)} frames")
    print("Results saved to output_videos/sam_coordinates_output.avi")

if __name__ == '__main__':
    main()
