import cv2
import numpy as np
import torch
from ultralytics import SAM, YOLO
import pickle
import os
import sys
sys.path.append('../')
from utils import get_center_of_bbox, get_foot_position

class SAMCoordinateExtractor:
    def __init__(self, sam_checkpoint_path, yolo_model_path, model_type="vit_h"):
        """
        Initialize SAM coordinate extractor

        Args:
            sam_checkpoint_path: Path to SAM model checkpoint (will be converted to Ultralytics format)
            yolo_model_path: Path to YOLO model for initial detection
            model_type: SAM model type ('vit_h', 'vit_l', 'vit_b')
        """
        # Map model types to Ultralytics SAM model names
        model_mapping = {
            'vit_h': 'sam_b.pt',  # Use sam_b.pt as it's more commonly available
            'vit_l': 'sam_b.pt',
            'vit_b': 'sam_b.pt'
        }

        # Initialize SAM using Ultralytics (will auto-download if needed)
        sam_model_name = model_mapping.get(model_type, 'sam_b.pt')
        print(f"Initializing SAM model: {sam_model_name}")
        self.sam = SAM(sam_model_name)

        # Initialize YOLO for initial detection
        self.yolo_model = YOLO(yolo_model_path)

        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    def extract_coordinates_from_frames(self, frames, save_stub=True, stub_path='stubs/sam_coordinates.pkl'):
        """
        Extract precise coordinates for ball, players, and pitch using SAM

        Args:
            frames: List of video frames
            save_stub: Whether to save results to pickle file
            stub_path: Path to save/load pickle file

        Returns:
            Dictionary containing coordinates for each frame
        """
        if os.path.exists(stub_path):
            with open(stub_path, 'rb') as f:
                return pickle.load(f)

        coordinates = {
            'players': [],
            'ball': [],
            'pitch': []
        }

        for frame_idx, frame in enumerate(frames):
            print(f"Processing frame {frame_idx + 1}/{len(frames)}")

            # Get initial detections from YOLO
            yolo_results = self.yolo_model.predict(frame, conf=0.1, verbose=False)[0]

            frame_coords = {
                'players': [],
                'ball': [],
                'pitch': self._extract_pitch_coordinates(frame)
            }

            # Process each detection
            for box in yolo_results.boxes:
                bbox = box.xyxy[0].cpu().numpy()
                cls_id = int(box.cls[0])
                cls_name = yolo_results.names[cls_id]

                if cls_name in ['player', 'goalkeeper']:
                    player_coords = self._extract_player_coordinates(bbox, frame)
                    if player_coords:
                        frame_coords['players'].append(player_coords)

                elif cls_name == 'ball':
                    ball_coords = self._extract_ball_coordinates(bbox, frame)
                    if ball_coords:
                        frame_coords['ball'].append(ball_coords)

            coordinates['players'].append(frame_coords['players'])
            coordinates['ball'].append(frame_coords['ball'])
            coordinates['pitch'].append(frame_coords['pitch'])

        if save_stub:
            os.makedirs(os.path.dirname(stub_path), exist_ok=True)
            with open(stub_path, 'wb') as f:
                pickle.dump(coordinates, f)

        return coordinates
    
    def _extract_player_coordinates(self, bbox, frame):
        """Extract precise player coordinates using SAM"""
        # Use bbox center as prompt point
        center_x = int((bbox[0] + bbox[2]) / 2)
        center_y = int((bbox[1] + bbox[3]) / 2)

        try:
            # Use Ultralytics SAM with point prompt
            results = self.sam(frame, points=[[center_x, center_y]], labels=[1])

            if results and len(results) > 0 and results[0].masks is not None:
                # Get the first (and typically best) mask
                mask = results[0].masks.data[0].cpu().numpy()

                # Extract coordinates from mask
                coords = self._mask_to_coordinates(mask)

                return {
                    'bbox': bbox.tolist(),
                    'mask_coordinates': coords,
                    'center': [center_x, center_y],
                    'foot_position': self._get_foot_from_mask(mask)
                }
        except Exception as e:
            print(f"Error extracting player coordinates: {e}")
            return None
    
    def _extract_ball_coordinates(self, bbox, frame):
        """Extract precise ball coordinates using SAM"""
        center_x = int((bbox[0] + bbox[2]) / 2)
        center_y = int((bbox[1] + bbox[3]) / 2)

        try:
            # Use Ultralytics SAM with point prompt
            results = self.sam(frame, points=[[center_x, center_y]], labels=[1])

            if results and len(results) > 0 and results[0].masks is not None:
                # Get the first (and typically best) mask
                mask = results[0].masks.data[0].cpu().numpy()
                coords = self._mask_to_coordinates(mask)

                return {
                    'bbox': bbox.tolist(),
                    'mask_coordinates': coords,
                    'center': [center_x, center_y],
                    'precise_center': self._get_mask_centroid(mask)
                }
        except Exception as e:
            print(f"Error extracting ball coordinates: {e}")
            return None
    
    def _extract_pitch_coordinates(self, frame):
        """Extract pitch/field coordinates using edge detection and SAM"""
        # Convert to HSV for better grass detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Define range for green grass
        lower_green = np.array([35, 40, 40])
        upper_green = np.array([85, 255, 255])
        
        # Create mask for green areas
        grass_mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # Find largest contour (likely the pitch)
        contours, _ = cv2.findContours(grass_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Extract corner points
            pitch_corners = [
                [x, y],  # top-left
                [x + w, y],  # top-right
                [x + w, y + h],  # bottom-right
                [x, y + h]  # bottom-left
            ]
            
            return {
                'corners': pitch_corners,
                'bounding_rect': [x, y, w, h],
                'contour': largest_contour.tolist()
            }
        
        return None
    
    def _mask_to_coordinates(self, mask):
        """Convert binary mask to list of coordinates"""
        coords = np.where(mask)
        return list(zip(coords[1].tolist(), coords[0].tolist()))  # (x, y) format
    
    def _get_foot_from_mask(self, mask):
        """Get foot position from player mask"""
        coords = np.where(mask)
        if len(coords[0]) > 0:
            # Get bottom-most point
            bottom_y = np.max(coords[0])
            bottom_x_indices = np.where(coords[0] == bottom_y)[0]
            bottom_x = np.mean(coords[1][bottom_x_indices])
            return [int(bottom_x), int(bottom_y)]
        return None
    
    def _get_mask_centroid(self, mask):
        """Get centroid of mask"""
        coords = np.where(mask)
        if len(coords[0]) > 0:
            center_y = np.mean(coords[0])
            center_x = np.mean(coords[1])
            return [int(center_x), int(center_y)]
        return None
    
    def visualize_coordinates(self, frame, coordinates, frame_idx=0):
        """Visualize extracted coordinates on frame"""
        vis_frame = frame.copy()
        
        # Draw players
        for player in coordinates['players'][frame_idx]:
            if player:
                # Draw bounding box
                bbox = player['bbox']
                cv2.rectangle(vis_frame, (int(bbox[0]), int(bbox[1])), 
                            (int(bbox[2]), int(bbox[3])), (0, 255, 0), 2)
                
                # Draw foot position
                if player['foot_position']:
                    cv2.circle(vis_frame, tuple(player['foot_position']), 5, (0, 255, 0), -1)
        
        # Draw ball
        for ball in coordinates['ball'][frame_idx]:
            if ball:
                center = ball['precise_center']
                cv2.circle(vis_frame, tuple(center), 8, (0, 0, 255), -1)
        
        # Draw pitch corners
        if coordinates['pitch'][frame_idx]:
            corners = coordinates['pitch'][frame_idx]['corners']
            for corner in corners:
                cv2.circle(vis_frame, tuple(corner), 10, (255, 0, 0), -1)
        
        return vis_frame