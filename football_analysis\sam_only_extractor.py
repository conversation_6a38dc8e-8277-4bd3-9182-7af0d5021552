import cv2
import numpy as np
import torch
from ultralytics import SAM
import pickle
import os
import sys
sys.path.append('../')
from utils import read_video, save_video

class SAMOnlyExtractor:
    def __init__(self, model_type="sam_b"):
        """
        Initialize SAM-only extractor
        
        Args:
            model_type: SAM model type ('sam_b', 'sam_l', 'sam_h', 'sam2_b', 'sam2_l', 'sam2_t', 'sam2_s')
        """
        print(f"Initializing SAM model: {model_type}.pt")
        self.sam = SAM(f"{model_type}.pt")
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
    
    def segment_everything(self, frames, save_stub=True, stub_path='stubs/sam_everything.pkl'):
        """
        Segment everything in the frames using SAM
        
        Args:
            frames: List of video frames
            save_stub: Whether to save results to pickle file
            stub_path: Path to save/load pickle file
            
        Returns:
            List of segmentation results for each frame
        """
        if os.path.exists(stub_path):
            print(f"Loading existing results from {stub_path}")
            with open(stub_path, 'rb') as f:
                return pickle.load(f)
        
        all_results = []
        
        for frame_idx, frame in enumerate(frames):
            print(f"Processing frame {frame_idx + 1}/{len(frames)}")
            
            try:
                # Segment everything in the frame
                results = self.sam(frame)
                
                if results and len(results) > 0:
                    frame_data = {
                        'masks': [],
                        'boxes': [],
                        'scores': []
                    }
                    
                    result = results[0]
                    if result.masks is not None:
                        # Extract mask data
                        masks = result.masks.data.cpu().numpy()
                        
                        for i, mask in enumerate(masks):
                            mask_coords = self._mask_to_coordinates(mask)
                            bbox = self._mask_to_bbox(mask)
                            
                            frame_data['masks'].append({
                                'coordinates': mask_coords,
                                'bbox': bbox,
                                'mask_id': i
                            })
                    
                    all_results.append(frame_data)
                else:
                    all_results.append({'masks': [], 'boxes': [], 'scores': []})
                    
            except Exception as e:
                print(f"Error processing frame {frame_idx}: {e}")
                all_results.append({'masks': [], 'boxes': [], 'scores': []})
        
        if save_stub:
            os.makedirs(os.path.dirname(stub_path), exist_ok=True)
            with open(stub_path, 'wb') as f:
                pickle.dump(all_results, f)
            print(f"Results saved to {stub_path}")
        
        return all_results
    
    def segment_with_points(self, frame, points, labels=None):
        """
        Segment objects based on point prompts
        
        Args:
            frame: Input frame
            points: List of [x, y] coordinates
            labels: List of labels (1 for positive, 0 for negative)
            
        Returns:
            Segmentation results
        """
        if labels is None:
            labels = [1] * len(points)  # Default to positive points
        
        try:
            results = self.sam(frame, points=points, labels=labels)
            return results
        except Exception as e:
            print(f"Error in point-based segmentation: {e}")
            return None
    
    def segment_with_bbox(self, frame, bbox):
        """
        Segment object based on bounding box prompt
        
        Args:
            frame: Input frame
            bbox: Bounding box [x1, y1, x2, y2]
            
        Returns:
            Segmentation results
        """
        try:
            results = self.sam(frame, bboxes=[bbox])
            return results
        except Exception as e:
            print(f"Error in bbox-based segmentation: {e}")
            return None
    
    def _mask_to_coordinates(self, mask):
        """Convert binary mask to list of coordinates"""
        coords = np.where(mask)
        return list(zip(coords[1].tolist(), coords[0].tolist()))  # (x, y) format
    
    def _mask_to_bbox(self, mask):
        """Extract bounding box from mask"""
        coords = np.where(mask)
        if len(coords[0]) > 0:
            y_min, y_max = np.min(coords[0]), np.max(coords[0])
            x_min, x_max = np.min(coords[1]), np.max(coords[1])
            return [int(x_min), int(y_min), int(x_max), int(y_max)]
        return None
    
    def visualize_results(self, frame, results, frame_idx=0):
        """Visualize segmentation results on frame"""
        vis_frame = frame.copy()
        
        if frame_idx < len(results):
            frame_data = results[frame_idx]
            
            # Draw masks with different colors
            colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), 
                     (255, 0, 255), (0, 255, 255), (128, 128, 128)]
            
            for i, mask_data in enumerate(frame_data['masks'][:10]):  # Limit to first 10 masks
                color = colors[i % len(colors)]
                bbox = mask_data['bbox']
                
                if bbox:
                    # Draw bounding box
                    cv2.rectangle(vis_frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
                    
                    # Add mask ID label
                    cv2.putText(vis_frame, f"M{mask_data['mask_id']}", 
                              (bbox[0], bbox[1] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return vis_frame
