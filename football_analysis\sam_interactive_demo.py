import os
import cv2
import numpy as np
from ultralytics import SAM

def demo_sam_capabilities():
    """
    Demonstrate different SAM capabilities without YOLO
    """
    print("=== SAM Interactive Demo ===")
    print("This demo shows how to use SAM without YOLO for different segmentation tasks.")
    
    # Initialize SAM
    print("\nInitializing SAM model...")
    sam = SAM("sam_b.pt")  # Will auto-download if not present
    
    # Get script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Load a sample image (first frame from video)
    from utils import read_video
    video_path = os.path.join(script_dir, 'input_videos', '2e57b9_0.mp4')
    
    if os.path.exists(video_path):
        print(f"Loading sample frame from: {video_path}")
        frames = read_video(video_path)
        if frames:
            image = frames[0]
        else:
            print("Could not load video frames")
            return
    else:
        print(f"Video file not found: {video_path}")
        return
    
    # Demo 1: Segment Everything
    print("\n=== Demo 1: Segment Everything ===")
    print("SAM will automatically find and segment all objects in the image...")
    
    results = sam(image)
    if results and len(results) > 0 and results[0].masks is not None:
        masks = results[0].masks.data.cpu().numpy()
        print(f"Found {len(masks)} objects/segments")
        
        # Create visualization
        vis_image = image.copy()
        colors = np.random.randint(0, 255, (len(masks), 3))
        
        for i, mask in enumerate(masks[:10]):  # Show first 10 masks
            # Create colored overlay
            color_mask = np.zeros_like(image)
            color_mask[mask > 0] = colors[i]
            vis_image = cv2.addWeighted(vis_image, 0.7, color_mask, 0.3, 0)
        
        output_path = os.path.join(script_dir, 'output_videos', 'sam_demo_everything.jpg')
        cv2.imwrite(output_path, vis_image)
        print(f"Saved result to: {output_path}")
    
    # Demo 2: Point-based Segmentation
    print("\n=== Demo 2: Point-based Segmentation ===")
    print("Segment objects by clicking on them (simulated with predefined points)...")
    
    # Define some example points on the football field
    height, width = image.shape[:2]
    example_points = [
        [width//4, height//2],      # Left side
        [width//2, height//3],      # Top center  
        [3*width//4, height//2],    # Right side
        [width//2, 2*height//3],    # Bottom center
    ]
    
    vis_image = image.copy()
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
    
    for i, point in enumerate(example_points):
        print(f"Segmenting object at point {point}...")
        
        # Segment with point prompt
        results = sam(image, points=[point], labels=[1])
        
        if results and len(results) > 0 and results[0].masks is not None:
            mask = results[0].masks.data[0].cpu().numpy()
            
            # Draw point
            cv2.circle(vis_image, tuple(point), 8, colors[i], -1)
            cv2.circle(vis_image, tuple(point), 10, (255, 255, 255), 2)
            
            # Draw mask contour
            contours, _ = cv2.findContours(
                mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )
            cv2.drawContours(vis_image, contours, -1, colors[i], 3)
    
    output_path = os.path.join(script_dir, 'output_videos', 'sam_demo_points.jpg')
    cv2.imwrite(output_path, vis_image)
    print(f"Saved result to: {output_path}")
    
    # Demo 3: Bounding Box Segmentation
    print("\n=== Demo 3: Bounding Box Segmentation ===")
    print("Segment objects by providing bounding boxes...")
    
    # Define example bounding boxes
    example_boxes = [
        [100, 100, 300, 400],       # Left area
        [400, 150, 600, 350],       # Center area
        [650, 200, 850, 500],       # Right area
    ]
    
    vis_image = image.copy()
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]
    
    for i, bbox in enumerate(example_boxes):
        print(f"Segmenting object in bbox {bbox}...")
        
        # Segment with bbox prompt
        results = sam(image, bboxes=[bbox])
        
        if results and len(results) > 0 and results[0].masks is not None:
            mask = results[0].masks.data[0].cpu().numpy()
            
            # Draw bounding box
            cv2.rectangle(vis_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), colors[i], 3)
            
            # Draw mask contour
            contours, _ = cv2.findContours(
                mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )
            cv2.drawContours(vis_image, contours, -1, colors[i], 2)
    
    output_path = os.path.join(script_dir, 'output_videos', 'sam_demo_boxes.jpg')
    cv2.imwrite(output_path, vis_image)
    print(f"Saved result to: {output_path}")
    
    print("\n=== Demo Complete! ===")
    print("SAM can be used in many ways:")
    print("1. Automatic segmentation of everything in an image")
    print("2. Interactive segmentation with point clicks")
    print("3. Segmentation with bounding box prompts")
    print("4. Combining positive and negative points for fine-tuning")
    print("5. Processing video frames for temporal segmentation")
    print("\nCheck the output_videos folder for the generated results!")

if __name__ == '__main__':
    demo_sam_capabilities()
