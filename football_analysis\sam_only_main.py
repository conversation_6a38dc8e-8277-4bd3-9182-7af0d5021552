import os
import numpy as np
from utils import read_video, save_video
from sam_only_extractor import SAMOnlyExtractor

def main():
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Initialize SAM-only extractor
    # Available models: sam_b, sam_l, sam_h, sam2_b, sam2_l, sam2_t, sam2_s
    print("Initializing SAM extractor...")
    sam_extractor = SAMOnlyExtractor(model_type="sam_b")  # Using SAM base model
    
    # Read video frames
    video_path = os.path.join(script_dir, 'input_videos', '2e57b9_0.mp4')
    print(f"Reading video from: {video_path}")
    video_frames = read_video(video_path)
    
    # Process only first 10 frames for demo (SAM without YOLO can be slow)
    num_frames = min(10, len(video_frames))
    print(f"Processing {num_frames} frames...")
    
    # Method 1: Segment everything automatically
    print("\n=== Method 1: Segment Everything ===")
    results = sam_extractor.segment_everything(
        video_frames[:num_frames],
        save_stub=True,
        stub_path=os.path.join(script_dir, 'stubs', 'sam_everything.pkl')
    )
    
    # Visualize results
    output_frames = []
    for frame_idx, frame in enumerate(video_frames[:num_frames]):
        vis_frame = sam_extractor.visualize_results(frame, results, frame_idx)
        output_frames.append(vis_frame)
    
    # Save output video
    output_path = os.path.join(script_dir, 'output_videos', 'sam_only_output.avi')
    save_video(output_frames, output_path)
    
    # Print summary
    total_masks = sum(len(frame_data['masks']) for frame_data in results)
    avg_masks = total_masks / len(results) if results else 0
    print(f"\nProcessed {len(results)} frames")
    print(f"Total masks detected: {total_masks}")
    print(f"Average masks per frame: {avg_masks:.2f}")
    print(f"Results saved to {output_path}")
    
    # Method 2: Interactive segmentation with point prompts (example)
    print("\n=== Method 2: Point-based Segmentation Example ===")
    if len(video_frames) > 0:
        # Example: segment objects at specific points in the first frame
        frame = video_frames[0]
        
        # Define some example points (you can modify these)
        # Points should be [x, y] coordinates where you want to segment objects
        example_points = [
            [400, 300],  # Center-left area
            [600, 400],  # Center-right area
            [500, 200],  # Top-center area
        ]
        
        print(f"Segmenting objects at points: {example_points}")
        point_results = sam_extractor.segment_with_points(frame, example_points)
        
        if point_results and len(point_results) > 0:
            # Visualize point-based segmentation
            vis_frame = frame.copy()
            result = point_results[0]
            
            if result.masks is not None:
                masks = result.masks.data.cpu().numpy()
                colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]
                
                for i, (point, mask) in enumerate(zip(example_points, masks)):
                    # Draw the point
                    cv2.circle(vis_frame, tuple(point), 8, colors[i % len(colors)], -1)
                    
                    # Draw mask boundary
                    contours, _ = cv2.findContours(
                        mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
                    )
                    cv2.drawContours(vis_frame, contours, -1, colors[i % len(colors)], 2)
                
                # Save point-based result
                point_output_path = os.path.join(script_dir, 'output_videos', 'sam_point_based.jpg')
                cv2.imwrite(point_output_path, vis_frame)
                print(f"Point-based segmentation saved to {point_output_path}")
    
    print("\n=== SAM-only processing complete! ===")
    print("You can use SAM in several ways:")
    print("1. Segment everything automatically (as shown above)")
    print("2. Segment with point prompts (click on objects)")
    print("3. Segment with bounding box prompts")
    print("4. Use negative points to exclude areas")

if __name__ == '__main__':
    import cv2  # Import here to avoid issues if not available
    main()
